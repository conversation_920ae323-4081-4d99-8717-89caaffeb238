<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tolerance_lateness', function (Blueprint $table) {
            $table->id();
            $table->foreignId('karyawan_id')->constrained('karyawan')->onDelete('cascade');
            $table->foreignId('absensi_id')->constrained('absensi')->onDelete('cascade');
            $table->text('alasan')->comment('Alasan pemberian toleransi keterlambatan');
            $table->enum('status_pengampunan', ['pending', 'approved', 'rejected'])->default('pending');
            $table->integer('menit_toleransi')->comment('Jumlah menit yang ditoleransi');
            $table->foreignId('created_by')->constrained('users')->comment('User yang mengajukan toleransi');
            $table->foreignId('approved_by')->nullable()->constrained('users')->comment('User yang menyetujui toleransi');
            $table->timestamp('approved_at')->nullable()->comment('Waktu persetujuan');
            $table->text('catatan_approval')->nullable()->comment('Catatan dari approver');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Index untuk performa
            $table->index(['karyawan_id', 'absensi_id']);
            $table->index(['status_pengampunan', 'is_active']);
            $table->index('created_by');
            $table->index('approved_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tolerance_lateness');
    }
};
