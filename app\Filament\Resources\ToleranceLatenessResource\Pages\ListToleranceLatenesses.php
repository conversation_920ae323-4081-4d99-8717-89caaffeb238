<?php

namespace App\Filament\Resources\ToleranceLatenessResource\Pages;

use App\Filament\Resources\ToleranceLatenessResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListToleranceLatenesses extends ListRecords
{
    protected static string $resource = ToleranceLatenessResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
