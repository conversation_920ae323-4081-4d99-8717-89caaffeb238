# Fitur Dependency Status Divisi-Departemen

## Deskripsi
Fitur ini menambahkan kemampuan untuk memantau dan mengelola konsistensi data antara divisi dan departemen dalam sistem HRD. Fitur ini membantu memastikan bahwa semua karyawan dalam suatu divisi memiliki departemen yang sesuai dengan departemen divisi tersebut.

## Fitur yang Ditambahkan

### 1. Kolom Dependency Status
- **Icon Column**: Menampilkan status dependency dengan icon check (✓) untuk konsisten dan X (✗) untuk tidak konsisten
- **Tooltip**: Memberikan penjelasan detail tentang status dependency
- **Warna**: Hijau untuk konsisten, merah untuk tidak konsisten

### 2. <PERSON><PERSON>m Ju<PERSON>wan
- Menampilkan total karyawan dalam divisi
- Ditampilkan dalam bentuk badge berwarna biru

### 3. <PERSON><PERSON><PERSON> Tidak Konsisten
- Menampilkan jumlah karyawan yang memiliki departemen berbeda dengan departemen divisi
- <PERSON><PERSON> muncul jika divisi memiliki karyawan
- Badge merah jika ada inkonsistensi, hijau jika konsisten

### 4. Filter Tambahan
- **Filter by Departemen**: Filter divisi berdasarkan departemen
- **Status Dependency**: Filter berdasarkan status konsistensi (Konsisten/Tidak Konsisten)
- **Status Karyawan**: Filter berdasarkan apakah divisi memiliki karyawan atau tidak

### 5. Action Perbaiki Dependency
- Tombol action untuk memperbaiki inkonsistensi data
- Hanya muncul jika ada karyawan yang tidak konsisten
- Memerlukan konfirmasi sebelum eksekusi
- Otomatis membuat record mutasi untuk tracking perubahan

## Cara Kerja

### Deteksi Inkonsistensi
Sistem mengecek apakah ada karyawan dalam divisi yang memiliki `id_departemen` berbeda dengan `departemen_id` dari divisi tersebut.

```php
$inconsistentEmployees = Karyawan::where('id_divisi', $record->id)
    ->where('id_departemen', '!=', $record->departemen_id)
    ->count();
```

### Perbaikan Otomatis
Ketika action "Perbaiki Dependency" dijalankan:
1. Sistem mengidentifikasi semua karyawan yang tidak konsisten
2. Memperbarui `id_departemen` karyawan sesuai dengan departemen divisi
3. Membuat record mutasi untuk tracking perubahan
4. Menonaktifkan record mutasi sebelumnya

### Tracking Perubahan
Setiap perubahan departemen karyawan akan dicatat dalam tabel `mutasi_promosi_demosi` dengan:
- Tipe: 'mutasi'
- Alasan: "Perbaikan dependency: Sinkronisasi departemen dengan divisi [nama_divisi]"
- Tanggal efektif: saat ini
- Status aktif: true

## Manfaat

1. **Konsistensi Data**: Memastikan data karyawan konsisten dengan struktur organisasi
2. **Monitoring**: Mudah memantau divisi mana yang memiliki inkonsistensi data
3. **Perbaikan Cepat**: Satu klik untuk memperbaiki semua inkonsistensi dalam divisi
4. **Audit Trail**: Semua perubahan tercatat dalam sistem mutasi
5. **Filter & Search**: Mudah mencari dan memfilter data berdasarkan status dependency

## Penggunaan

1. **Melihat Status**: Buka halaman Data Divisi, lihat kolom "Dependency Status"
2. **Filter Data**: Gunakan filter "Status Dependency" untuk melihat divisi yang bermasalah
3. **Perbaiki Inkonsistensi**: Klik tombol "Perbaiki Dependency" pada divisi yang bermasalah
4. **Konfirmasi**: Sistem akan menampilkan jumlah karyawan yang akan dipindahkan
5. **Eksekusi**: Klik konfirmasi untuk menjalankan perbaikan

## Catatan Penting

- Fitur ini tidak mengubah data yang sudah konsisten
- Semua perubahan akan tercatat dalam sistem mutasi
- Perbaikan dependency akan mempengaruhi data karyawan secara permanen
- Disarankan untuk backup data sebelum melakukan perbaikan massal
