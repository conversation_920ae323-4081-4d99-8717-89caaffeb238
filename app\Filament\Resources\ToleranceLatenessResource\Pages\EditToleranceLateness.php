<?php

namespace App\Filament\Resources\ToleranceLatenessResource\Pages;

use App\Filament\Resources\ToleranceLatenessResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditToleranceLateness extends EditRecord
{
    protected static string $resource = ToleranceLatenessResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
