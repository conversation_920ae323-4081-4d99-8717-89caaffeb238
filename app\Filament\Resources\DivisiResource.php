<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Divisi;
use App\Models\Departemen;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\DivisiResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasExportActions;
use App\Exports\DivisiExport;
use App\Filament\Resources\DivisiResource\RelationManagers;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use App\Models\Karyawan;
use Illuminate\Support\Facades\DB;

class DivisiResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = Divisi::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $label = 'Data Divisi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('departemen_id')
                    ->label('Departemen')
                    ->options(Departemen::query()->pluck('nama_departemen', 'id'))
                    ->searchable()
                    ->required()
                    ->afterStateUpdated(function ($state, $old, $record) {
                        // Show warning if departemen changed and there are employees
                        if ($record && $old && $state !== $old) {
                            $employeeCount = Karyawan::where('id_divisi', $record->id)->count();
                            if ($employeeCount > 0) {
                                Notification::make()
                                    ->title('Perhatian!')
                                    ->body("Perubahan departemen akan mempengaruhi {$employeeCount} karyawan di divisi ini.")
                                    ->warning()
                                    ->send();
                            }
                        }
                    }),

                TextInput::make('nama_divisi')
                    ->label('Nama Divisi')
                    ->required(),

                TextInput::make('deskripsi')
                    ->label('Deskripsi')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('departemen.nama_departemen')
                    ->label('Departemen')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('nama_divisi')
                    ->label('Nama Divisi')
                    ->sortable()
                    ->searchable(),

                IconColumn::make('is_dependent')
                    ->label('Dependency Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->tooltip(fn($record) => $record->is_dependent
                        ? 'Semua karyawan di divisi ini konsisten dengan departemen divisi'
                        : 'Ada inkonsistensi: beberapa karyawan memiliki departemen berbeda dengan departemen divisi')
                    ->getStateUsing(function ($record) {
                        // Check if all employees in this division have the same department as the division's department
                        $inconsistentEmployees = Karyawan::where('id_divisi', $record->id)
                            ->where('id_departemen', '!=', $record->departemen_id)
                            ->count();

                        return $inconsistentEmployees === 0;
                    }),

                TextColumn::make('employee_count')
                    ->label('Jumlah Karyawan')
                    ->getStateUsing(function ($record) {
                        return Karyawan::where('id_divisi', $record->id)->count();
                    })
                    ->badge()
                    ->color('primary'),

                TextColumn::make('inconsistent_count')
                    ->label('Karyawan Tidak Konsisten')
                    ->getStateUsing(function ($record) {
                        return Karyawan::where('id_divisi', $record->id)
                            ->where('id_departemen', '!=', $record->departemen_id)
                            ->count();
                    })
                    ->badge()
                    ->color(fn($state) => $state > 0 ? 'danger' : 'success')
                    ->visible(fn($record) => Karyawan::where('id_divisi', $record->id)->count() > 0),

                TextColumn::make('deskripsi')
                    ->label('Deskripsi')
                    ->limit(40)
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('departemen_id')
                    ->label('Filter by Departemen')
                    ->options(Departemen::query()->pluck('nama_departemen', 'id'))
                    ->searchable(),

                SelectFilter::make('dependency_status')
                    ->label('Status Dependency')
                    ->options([
                        'consistent' => 'Konsisten',
                        'inconsistent' => 'Tidak Konsisten',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'] === 'consistent',
                            fn(Builder $query): Builder => $query->whereDoesntHave('karyawan', function ($q) {
                                $q->whereColumn('id_departemen', '!=', 'divisi.departemen_id');
                            })
                        )->when(
                            $data['value'] === 'inconsistent',
                            fn(Builder $query): Builder => $query->whereHas('karyawan', function ($q) {
                                $q->whereColumn('id_departemen', '!=', 'divisi.departemen_id');
                            })
                        );
                    }),

                SelectFilter::make('has_employees')
                    ->label('Status Karyawan')
                    ->options([
                        'with_employees' => 'Memiliki Karyawan',
                        'without_employees' => 'Tanpa Karyawan',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'] === 'with_employees',
                            fn(Builder $query): Builder => $query->has('karyawan')
                        )->when(
                            $data['value'] === 'without_employees',
                            fn(Builder $query): Builder => $query->doesntHave('karyawan')
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('fix_dependency')
                    ->label('Perbaiki Dependency')
                    ->icon('heroicon-o-wrench-screwdriver')
                    ->color('warning')
                    ->visible(function ($record) {
                        // Only show if there are inconsistent employees
                        return Karyawan::where('id_divisi', $record->id)
                            ->where('id_departemen', '!=', $record->departemen_id)
                            ->count() > 0;
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Perbaiki Dependency Divisi')
                    ->modalDescription(function ($record) {
                        $inconsistentCount = Karyawan::where('id_divisi', $record->id)
                            ->where('id_departemen', '!=', $record->departemen_id)
                            ->count();
                        return "Akan memindahkan {$inconsistentCount} karyawan ke departemen {$record->departemen->nama_departemen} sesuai dengan divisi {$record->nama_divisi}.";
                    })
                    ->action(function ($record) {
                        $inconsistentEmployees = Karyawan::where('id_divisi', $record->id)
                            ->where('id_departemen', '!=', $record->departemen_id)
                            ->get();

                        if ($inconsistentEmployees->count() > 0) {
                            DB::transaction(function () use ($inconsistentEmployees, $record) {
                                foreach ($inconsistentEmployees as $karyawan) {
                                    $karyawan->update(['id_departemen' => $record->departemen_id]);

                                    // Create mutasi record for departemen change
                                    \App\Models\MutasiPromosiDemosi::create([
                                        'karyawan_id' => $karyawan->id,
                                        'tipe' => 'mutasi',
                                        'entitas_id' => $karyawan->id_entitas,
                                        'departemen_id' => $record->departemen_id,
                                        'divisi_id' => $karyawan->id_divisi,
                                        'jabatan_id' => $karyawan->id_jabatan,
                                        'tanggal_efektif' => now(),
                                        'alasan' => "Perbaikan dependency: Sinkronisasi departemen dengan divisi {$record->nama_divisi}",
                                        'is_active' => true,
                                    ]);

                                    // Deactivate previous mutasi
                                    \App\Models\MutasiPromosiDemosi::where('karyawan_id', $karyawan->id)
                                        ->where('id', '!=', \App\Models\MutasiPromosiDemosi::latest()->first()->id)
                                        ->update(['is_active' => false]);
                                }
                            });

                            Notification::make()
                                ->title('Dependency Diperbaiki')
                                ->body("Berhasil memperbaiki dependency untuk {$inconsistentEmployees->count()} karyawan")
                                ->success()
                                ->send();
                        }
                    }),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(DivisiExport::class, 'Data Divisi'),
            ])
            ->defaultSort('nama_divisi');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDivisis::route('/'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->with('departemen:id,nama_departemen');
    }

    public static function afterSave($record, array $data): void
    {
        // Check if departemen_id changed
        if ($record->wasChanged('departemen_id')) {
            $oldDepartemenId = $record->getOriginal('departemen_id');
            $newDepartemenId = $record->departemen_id;

            // Update all karyawan in this divisi to new departemen
            $affectedEmployees = Karyawan::where('id_divisi', $record->id)
                ->where('id_departemen', $oldDepartemenId)
                ->get();

            if ($affectedEmployees->count() > 0) {
                DB::transaction(function () use ($affectedEmployees, $newDepartemenId, $record) {
                    foreach ($affectedEmployees as $karyawan) {
                        $karyawan->update(['id_departemen' => $newDepartemenId]);

                        // Create mutasi record for departemen change
                        \App\Models\MutasiPromosiDemosi::create([
                            'karyawan_id' => $karyawan->id,
                            'tipe' => 'mutasi',
                            'entitas_id' => $karyawan->id_entitas,
                            'departemen_id' => $newDepartemenId,
                            'divisi_id' => $karyawan->id_divisi,
                            'jabatan_id' => $karyawan->id_jabatan,
                            'tanggal_efektif' => now(),
                            'alasan' => "Otomatis: Divisi {$record->nama_divisi} dipindah ke departemen baru",
                            'is_active' => true,
                        ]);

                        // Deactivate previous mutasi
                        \App\Models\MutasiPromosiDemosi::where('karyawan_id', $karyawan->id)
                            ->where('id', '!=', \App\Models\MutasiPromosiDemosi::latest()->first()->id)
                            ->update(['is_active' => false]);
                    }
                });

                $departemenBaru = Departemen::find($newDepartemenId);

                Notification::make()
                    ->title('Update Berhasil')
                    ->body("Berhasil memindahkan {$affectedEmployees->count()} karyawan ke departemen {$departemenBaru->nama_departemen}")
                    ->success()
                    ->send();
            }
        }
    }
}
