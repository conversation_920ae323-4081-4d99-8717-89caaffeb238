<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensi', function (Blueprint $table) {
            $table->boolean('is_tolerance_given')->default(false)->comment('Apakah diberikan toleransi keterlambatan');
            $table->text('tolerance_reason')->nullable()->comment('Alasan pemberian toleransi');
            $table->foreignId('tolerance_approved_by')->nullable()->constrained('users')->comment('User yang menyetujui toleransi');
            $table->timestamp('tolerance_approved_at')->nullable()->comment('Waktu persetujuan toleransi');

            // Index untuk performa
            $table->index('is_tolerance_given');
            $table->index('tolerance_approved_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensi', function (Blueprint $table) {
            $table->dropForeign(['tolerance_approved_by']);
            $table->dropIndex(['is_tolerance_given']);
            $table->dropIndex(['tolerance_approved_by']);
            $table->dropColumn([
                'is_tolerance_given',
                'tolerance_reason',
                'tolerance_approved_by',
                'tolerance_approved_at'
            ]);
        });
    }
};
