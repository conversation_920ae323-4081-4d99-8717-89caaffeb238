<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SopDokumenResource\Pages;
use App\Models\SopDokumen;
use App\Models\Departemen;
use App\Models\Divisi;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use App\Traits\HasExportActions;
use App\Exports\SopDokumenExport;

class SopDokumenResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = SopDokumen::class;

    protected static ?string $navigationGroup = 'Dokumen & SOP';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'SOP Dokumen';

    protected static ?string $modelLabel = 'SOP Dokumen';

    protected static ?string $pluralModelLabel = 'SOP Dokumen';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi SOP')
                    ->schema([
                        Forms\Components\TextInput::make('judul_sop')
                            ->label('Judul SOP')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('scope_type')
                            ->label('Berlaku Untuk')
                            ->options([
                                'departemen' => 'Departemen',
                                'divisi' => 'Divisi',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                $set('departemen_id', null);
                                $set('divisi_id', null);
                            }),

                        Forms\Components\Select::make('departemen_id')
                            ->label('Departemen')
                            ->options(Departemen::pluck('nama_departemen', 'id'))
                            ->searchable()
                            ->visible(fn(callable $get) => $get('scope_type') === 'departemen')
                            ->required(fn(callable $get) => $get('scope_type') === 'departemen'),

                        Forms\Components\Select::make('divisi_id')
                            ->label('Divisi')
                            ->options(function (callable $get) {
                                if ($get('scope_type') === 'divisi') {
                                    return Divisi::with('departemen')
                                        ->get()
                                        ->mapWithKeys(function ($divisi) {
                                            return [$divisi->id => $divisi->nama_divisi . ' (' . $divisi->departemen?->nama_departemen . ')'];
                                        });
                                }
                                return [];
                            })
                            ->searchable()
                            ->visible(fn(callable $get) => $get('scope_type') === 'divisi')
                            ->required(fn(callable $get) => $get('scope_type') === 'divisi'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('File & Status')
                    ->schema([
                        Forms\Components\FileUpload::make('file_path')
                            ->label('File SOP (PDF)')
                            ->required()
                            ->directory('sop_dokumens')
                            ->acceptedFileTypes(['application/pdf'])
                            ->maxSize(10240) // 10MB
                            ->disk('public')
                            ->columnSpanFull(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'aktif' => 'Aktif',
                                'tidak_aktif' => 'Tidak Aktif',
                            ])
                            ->default('aktif')
                            ->required(),

                        Forms\Components\TextInput::make('versi')
                            ->label('Versi')
                            ->default('1.0')
                            ->required()
                            ->maxLength(10),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Periode Berlaku')
                    ->schema([
                        Forms\Components\DatePicker::make('tanggal_berlaku')
                            ->label('Tanggal Berlaku')
                            ->default(now())
                            ->required(),

                        Forms\Components\DatePicker::make('tanggal_berakhir')
                            ->label('Tanggal Berakhir')
                            ->after('tanggal_berlaku'),
                    ])
                    ->columns(2),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('judul_sop')
                    ->label('Judul SOP')
                    ->searchable()
                    ->sortable()
                    ->limit(40),

                Tables\Columns\TextColumn::make('scope_type')
                    ->label('Scope')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'departemen' => 'primary',
                        'divisi' => 'success',
                        default => 'secondary',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'departemen' => 'Departemen',
                        'divisi' => 'Divisi',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('scope_name')
                    ->label('Berlaku Untuk')
                    ->searchable(['departemen.nama_departemen', 'divisi.nama_divisi'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'aktif' => 'success',
                        'tidak_aktif' => 'danger',
                        default => 'secondary',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'aktif' => 'Aktif',
                        'tidak_aktif' => 'Tidak Aktif',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('versi')
                    ->label('Versi')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_berlaku')
                    ->label('Berlaku')
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_berakhir')
                    ->label('Berakhir')
                    ->date('d/m/Y')
                    ->placeholder('Tidak terbatas')
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('scope_type')
                    ->label('Scope')
                    ->options([
                        'departemen' => 'Departemen',
                        'divisi' => 'Divisi',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'aktif' => 'Aktif',
                        'tidak_aktif' => 'Tidak Aktif',
                    ]),

                Tables\Filters\SelectFilter::make('departemen_id')
                    ->label('Departemen')
                    ->options(Departemen::pluck('nama_departemen', 'id'))
                    ->searchable(),

                Tables\Filters\SelectFilter::make('divisi_id')
                    ->label('Divisi')
                    ->options(function () {
                        return Divisi::with('departemen')
                            ->get()
                            ->mapWithKeys(function ($divisi) {
                                return [$divisi->id => $divisi->nama_divisi . ' (' . $divisi->departemen?->nama_departemen . ')'];
                            });
                    })
                    ->searchable(),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(fn(SopDokumen $record): string => asset('storage/' . $record->file_path))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(SopDokumenExport::class, 'Data SOP'),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum Ada SOP')
            ->emptyStateDescription('Mulai dengan menambahkan SOP pertama untuk departemen atau divisi.')
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSopDokumens::route('/'),
            'create' => Pages\CreateSopDokumen::route('/create'),
            'view' => Pages\ViewSopDokumen::route('/{record}'),
            'edit' => Pages\EditSopDokumen::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->with(['departemen', 'divisi', 'creator']);
    }
}
