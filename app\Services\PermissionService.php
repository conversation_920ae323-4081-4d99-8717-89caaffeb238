<?php

namespace App\Services;

use App\Models\KaryawanPermission;
use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class PermissionService
{
    /**
     * Check if current user has permission for specific action
     */
    public static function hasPermission(string $permissionType, $targetData = null): bool
    {
        $user = Auth::user();
        if (!$user) return false;

        // Super admin always has permission
        if ($user->hasRole('super_admin')) {
            return true;
        }

        // Get karyawan for current user
        $karyawan = Karyawan::where('id_user', $user->id)->first();
        if (!$karyawan) return false;

        return KaryawanPermission::hasPermission($karyawan->id, $permissionType, $targetData);
    }

    /**
     * Get accessible karyawan IDs for current user
     */
    public static function getAccessibleKaryawanIds(string $permissionType): array
    {
        $user = Auth::user();
        if (!$user) return [];

        // Super admin can access all
        if ($user->hasRole('super_admin')) {
            return Karyawan::where('status_aktif', true)->pluck('id')->toArray();
        }

        // Get karyawan for current user
        $karyawan = Karyawan::where('id_user', $user->id)->first();
        if (!$karyawan) return [];

        return KaryawanPermission::getAccessibleDataIds($karyawan->id, $permissionType);
    }

    /**
     * Apply permission filter to query
     */
    public static function applyPermissionFilter(Builder $query, string $permissionType, string $karyawanIdField = 'karyawan_id'): Builder
    {
        $accessibleIds = self::getAccessibleKaryawanIds($permissionType);

        if (empty($accessibleIds)) {
            // No permission, return empty result
            return $query->whereRaw('1 = 0');
        }

        return $query->whereIn($karyawanIdField, $accessibleIds);
    }

    /**
     * Check if user can approve cuti/izin
     */
    public static function canApproveCuti($cutiIzin = null): bool
    {
        return self::hasPermission('approve_cuti', $cutiIzin?->karyawan);
    }

    /**
     * Check if user can view absensi
     */
    public static function canViewAbsensi($absensi = null): bool
    {
        return self::hasPermission('view_absensi', $absensi?->karyawan);
    }

    /**
     * Check if user can manage jadwal
     */
    public static function canManageJadwal($karyawan = null): bool
    {
        return self::hasPermission('manage_jadwal', $karyawan);
    }

    /**
     * Check if user can view payroll
     */
    public static function canViewPayroll($karyawan = null): bool
    {
        return self::hasPermission('view_payroll', $karyawan);
    }

    /**
     * Check if user can manage karyawan data
     */
    public static function canManageKaryawan($karyawan = null): bool
    {
        return self::hasPermission('manage_karyawan', $karyawan);
    }

    /**
     * Check if user can manage absensi
     */
    public static function canManageAbsensi($absensi = null): bool
    {
        return self::hasPermission('manage_absensi', $absensi?->karyawan);
    }

    /**
     * Get permission summary for a karyawan
     */
    public static function getPermissionSummary(int $karyawanId): array
    {
        $permissions = KaryawanPermission::where('karyawan_id', $karyawanId)
            ->where('is_active', true)
            ->with('karyawan')
            ->get();

        $summary = [];
        foreach (KaryawanPermission::PERMISSION_TYPES as $type => $label) {
            $userPermissions = $permissions->where('permission_type', $type);

            if ($userPermissions->isEmpty()) {
                $summary[$type] = [
                    'has_permission' => false,
                    'label' => $label,
                    'scopes' => []
                ];
            } else {
                $scopes = [];
                foreach ($userPermissions as $permission) {
                    $scopes[] = [
                        'scope_type' => $permission->scope_type,
                        'scope_label' => $permission->scope_type_label,
                        'scope_description' => $permission->scope_description,
                        'description' => $permission->description,
                    ];
                }

                $summary[$type] = [
                    'has_permission' => true,
                    'label' => $label,
                    'scopes' => $scopes
                ];
            }
        }

        return $summary;
    }

    /**
     * Create or update permission
     */
    public static function setPermission(array $data): KaryawanPermission
    {
        return KaryawanPermission::create([
            'karyawan_id' => $data['karyawan_id'],
            'permission_type' => $data['permission_type'],
            'scope_type' => $data['scope_type'],
            'scope_values' => $data['scope_values'] ?? null,
            'description' => $data['description'] ?? null,
            'created_by' => Auth::id(),
        ]);
    }

    /**
     * Remove permission
     */
    public static function removePermission(int $permissionId): bool
    {
        $permission = KaryawanPermission::find($permissionId);
        if (!$permission) return false;

        return $permission->delete();
    }

    /**
     * Toggle permission status
     */
    public static function togglePermission(int $permissionId): bool
    {
        $permission = KaryawanPermission::find($permissionId);
        if (!$permission) return false;

        $permission->is_active = !$permission->is_active;
        return $permission->save();
    }

    /**
     * Get users with specific permission
     */
    public static function getUsersWithPermission(string $permissionType): array
    {
        $permissions = KaryawanPermission::where('permission_type', $permissionType)
            ->where('is_active', true)
            ->with('karyawan.user')
            ->get();

        $users = [];
        foreach ($permissions as $permission) {
            if ($permission->karyawan && $permission->karyawan->user) {
                $users[] = [
                    'user' => $permission->karyawan->user,
                    'karyawan' => $permission->karyawan,
                    'scope' => $permission->scope_description,
                ];
            }
        }

        return $users;
    }
}
