<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ToleranceLateness extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'tolerance_lateness';

    protected $fillable = [
        'karyawan_id',
        'absensi_id',
        'alasan',
        'status_pengampunan',
        'menit_toleransi',
        'created_by',
        'approved_by',
        'approved_at',
        'catatan_approval',
        'is_active',
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected $dates = ['deleted_at'];

    // Relasi ke Karyawan
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class, 'karyawan_id');
    }

    // Relasi ke Absensi
    public function absensi()
    {
        return $this->belongsTo(Absensi::class, 'absensi_id');
    }

    // <PERSON>lasi ke User yang membuat
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relasi ke User yang menyetujui
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scope untuk status pending
    public function scopePending($query)
    {
        return $query->where('status_pengampunan', 'pending');
    }

    // Scope untuk status approved
    public function scopeApproved($query)
    {
        return $query->where('status_pengampunan', 'approved');
    }

    // Scope untuk status rejected
    public function scopeRejected($query)
    {
        return $query->where('status_pengampunan', 'rejected');
    }

    // Scope untuk yang aktif
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Method untuk approve toleransi
    public function approve($approvedBy, $catatan = null)
    {
        $this->update([
            'status_pengampunan' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
            'catatan_approval' => $catatan,
        ]);

        return $this;
    }

    // Method untuk reject toleransi
    public function reject($approvedBy, $catatan = null)
    {
        $this->update([
            'status_pengampunan' => 'rejected',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
            'catatan_approval' => $catatan,
        ]);

        return $this;
    }

    // Method untuk cek apakah sudah disetujui
    public function isApproved()
    {
        return $this->status_pengampunan === 'approved';
    }

    // Method untuk cek apakah ditolak
    public function isRejected()
    {
        return $this->status_pengampunan === 'rejected';
    }

    // Method untuk cek apakah masih pending
    public function isPending()
    {
        return $this->status_pengampunan === 'pending';
    }
}
