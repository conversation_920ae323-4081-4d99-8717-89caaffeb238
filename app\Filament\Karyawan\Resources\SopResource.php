<?php

namespace App\Filament\Karyawan\Resources;

use App\Filament\Karyawan\Resources\SopResource\Pages;
use App\Models\SopDokumen;
use App\Models\Karyawan;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class SopResource extends Resource
{
    protected static ?string $model = SopDokumen::class;

    protected static ?string $navigationGroup = 'Dokumen';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'SOP';

    protected static ?string $modelLabel = 'SOP';

    protected static ?string $pluralModelLabel = 'SOP';

    public static function canAccess(): bool
    {
        return Auth::user()->karyawan()->exists();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('judul_sop')
                    ->label('Judul SOP')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),

                Tables\Columns\TextColumn::make('deskripsi')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->wrap()
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('scope_type')
                    ->label('Berlaku Untuk')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'departemen' => 'primary',
                        'divisi' => 'success',
                        default => 'secondary',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'departemen' => 'Departemen',
                        'divisi' => 'Divisi',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('scope_name')
                    ->label('Unit')
                    ->searchable(['departemen.nama_departemen', 'divisi.nama_divisi'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('versi')
                    ->label('Versi')
                    ->badge()
                    ->color('gray')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_berlaku')
                    ->label('Berlaku Sejak')
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_berakhir')
                    ->label('Berlaku Hingga')
                    ->date('d/m/Y')
                    ->placeholder('Tidak terbatas')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_berlaku')
                    ->label('Status')
                    ->boolean()
                    ->getStateUsing(fn(SopDokumen $record): bool => $record->isBerlaku())
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('scope_type')
                    ->label('Berlaku Untuk')
                    ->options([
                        'departemen' => 'Departemen',
                        'divisi' => 'Divisi',
                    ]),

                Tables\Filters\Filter::make('berlaku')
                    ->label('SOP Berlaku')
                    ->query(fn(Builder $query): Builder => $query->aktif()->berlaku()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail SOP')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.sop-detail', compact('record'));
                    }),

                Tables\Actions\Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(fn(SopDokumen $record): string => asset('storage/' . $record->file_path))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('tanggal_berlaku', 'desc')
            ->emptyStateHeading('Belum Ada SOP')
            ->emptyStateDescription('Belum ada SOP yang tersedia untuk departemen atau divisi Anda.')
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSops::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // Dapatkan data karyawan yang sedang login
        $user = Auth::user();
        $karyawan = Karyawan::with(['departemen', 'divisi'])->where('id_user', $user->id)->first();

        return parent::getEloquentQuery()
            ->with(['departemen', 'divisi'])
            ->where('status', 'aktif')
            ->forKaryawan($karyawan);
    }

    public static function canCreate(): bool
    {
        return false; // Karyawan tidak bisa membuat SOP
    }

    public static function canEdit($record): bool
    {
        return false; // Karyawan tidak bisa edit SOP
    }

    public static function canDelete($record): bool
    {
        return false; // Karyawan tidak bisa hapus SOP
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
