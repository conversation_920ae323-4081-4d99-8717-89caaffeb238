<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Divisi;
use App\Models\Departemen;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Jabatan;
use App\Models\MutasiPromosiDemosi;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use App\Filament\Resources\DivisiResource;

class DivisiDependencyTest extends TestCase
{
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user with admin role
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin'
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_detect_consistent_division_department_relationship()
    {
        // Create test data
        $departemen = Departemen::factory()->create(['nama_departemen' => 'IT Department']);
        $divisi = Divisi::factory()->create([
            'nama_divisi' => 'Software Development',
            'departemen_id' => $departemen->id
        ]);
        
        $entitas = Entitas::factory()->create();
        $jabatan = Jabatan::factory()->create();
        
        // Create employees with consistent department
        $karyawan1 = Karyawan::factory()->create([
            'id_divisi' => $divisi->id,
            'id_departemen' => $departemen->id,
            'id_entitas' => $entitas->id,
            'id_jabatan' => $jabatan->id
        ]);
        
        $karyawan2 = Karyawan::factory()->create([
            'id_divisi' => $divisi->id,
            'id_departemen' => $departemen->id,
            'id_entitas' => $entitas->id,
            'id_jabatan' => $jabatan->id
        ]);

        // Check dependency status
        $inconsistentCount = Karyawan::where('id_divisi', $divisi->id)
            ->where('id_departemen', '!=', $divisi->departemen_id)
            ->count();

        $this->assertEquals(0, $inconsistentCount);
    }

    /** @test */
    public function it_can_detect_inconsistent_division_department_relationship()
    {
        // Create test data
        $departemen1 = Departemen::factory()->create(['nama_departemen' => 'IT Department']);
        $departemen2 = Departemen::factory()->create(['nama_departemen' => 'HR Department']);
        
        $divisi = Divisi::factory()->create([
            'nama_divisi' => 'Software Development',
            'departemen_id' => $departemen1->id
        ]);
        
        $entitas = Entitas::factory()->create();
        $jabatan = Jabatan::factory()->create();
        
        // Create employees with inconsistent department
        $karyawan1 = Karyawan::factory()->create([
            'id_divisi' => $divisi->id,
            'id_departemen' => $departemen1->id, // Consistent
            'id_entitas' => $entitas->id,
            'id_jabatan' => $jabatan->id
        ]);
        
        $karyawan2 = Karyawan::factory()->create([
            'id_divisi' => $divisi->id,
            'id_departemen' => $departemen2->id, // Inconsistent
            'id_entitas' => $entitas->id,
            'id_jabatan' => $jabatan->id
        ]);

        // Check dependency status
        $inconsistentCount = Karyawan::where('id_divisi', $divisi->id)
            ->where('id_departemen', '!=', $divisi->departemen_id)
            ->count();

        $this->assertEquals(1, $inconsistentCount);
    }

    /** @test */
    public function it_can_fix_dependency_inconsistency()
    {
        // Create test data
        $departemen1 = Departemen::factory()->create(['nama_departemen' => 'IT Department']);
        $departemen2 = Departemen::factory()->create(['nama_departemen' => 'HR Department']);
        
        $divisi = Divisi::factory()->create([
            'nama_divisi' => 'Software Development',
            'departemen_id' => $departemen1->id
        ]);
        
        $entitas = Entitas::factory()->create();
        $jabatan = Jabatan::factory()->create();
        
        // Create employee with inconsistent department
        $karyawan = Karyawan::factory()->create([
            'id_divisi' => $divisi->id,
            'id_departemen' => $departemen2->id, // Wrong department
            'id_entitas' => $entitas->id,
            'id_jabatan' => $jabatan->id
        ]);

        // Simulate fixing dependency
        $inconsistentEmployees = Karyawan::where('id_divisi', $divisi->id)
            ->where('id_departemen', '!=', $divisi->departemen_id)
            ->get();

        foreach ($inconsistentEmployees as $employee) {
            $employee->update(['id_departemen' => $divisi->departemen_id]);
            
            // Create mutasi record
            MutasiPromosiDemosi::create([
                'karyawan_id' => $employee->id,
                'tipe' => 'mutasi',
                'entitas_id' => $employee->id_entitas,
                'departemen_id' => $divisi->departemen_id,
                'divisi_id' => $employee->id_divisi,
                'jabatan_id' => $employee->id_jabatan,
                'tanggal_efektif' => now(),
                'alasan' => "Perbaikan dependency: Sinkronisasi departemen dengan divisi {$divisi->nama_divisi}",
                'is_active' => true,
            ]);
        }

        // Verify fix
        $karyawan->refresh();
        $this->assertEquals($departemen1->id, $karyawan->id_departemen);
        
        // Verify mutasi record created
        $mutasi = MutasiPromosiDemosi::where('karyawan_id', $karyawan->id)->first();
        $this->assertNotNull($mutasi);
        $this->assertEquals('mutasi', $mutasi->tipe);
        $this->assertEquals($departemen1->id, $mutasi->departemen_id);
        $this->assertTrue($mutasi->is_active);
    }

    /** @test */
    public function it_shows_correct_employee_count()
    {
        // Create test data
        $departemen = Departemen::factory()->create();
        $divisi = Divisi::factory()->create(['departemen_id' => $departemen->id]);
        $entitas = Entitas::factory()->create();
        $jabatan = Jabatan::factory()->create();
        
        // Create multiple employees
        Karyawan::factory()->count(3)->create([
            'id_divisi' => $divisi->id,
            'id_departemen' => $departemen->id,
            'id_entitas' => $entitas->id,
            'id_jabatan' => $jabatan->id
        ]);

        $employeeCount = Karyawan::where('id_divisi', $divisi->id)->count();
        $this->assertEquals(3, $employeeCount);
    }

    /** @test */
    public function it_handles_division_without_employees()
    {
        // Create division without employees
        $departemen = Departemen::factory()->create();
        $divisi = Divisi::factory()->create(['departemen_id' => $departemen->id]);

        $employeeCount = Karyawan::where('id_divisi', $divisi->id)->count();
        $inconsistentCount = Karyawan::where('id_divisi', $divisi->id)
            ->where('id_departemen', '!=', $divisi->departemen_id)
            ->count();

        $this->assertEquals(0, $employeeCount);
        $this->assertEquals(0, $inconsistentCount);
    }
}
